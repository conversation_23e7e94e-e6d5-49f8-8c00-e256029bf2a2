# Copyright 2025 New Vector Ltd
#
# SPDX-License-Identifier: AGPL-3.0-only

name: Artifact Hub Metadata

on:
  pull_request_target:
  push:
    branches:
    - main
  workflow_dispatch:

jobs:
  artifact-hub:
    permissions:
      contents: read
      packages: write
    runs-on: ubuntu-latest
    steps:
    # This will push the OCI artifact only on merges
    # As the checkout will push the PR Target commit hash
    - name: Checkout
      uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683  # v4

    - uses: oras-project/setup-oras@8d34698a59f5ffe24821f0b48ab62a3de8b64b20  # v1

    - name: ORAS Login
      env:
        ORAS_USERNAME: ${{ github.actor }}
        ORAS_PASSWORD: ${{ github.token }}
      run: |
          oras login ghcr.io -u "$ORAS_USERNAME" -p "$ORAS_PASSWORD"

    - name: Push artifact-hub
      env:
        GITHUB_REPOSITORY: ${{ github.repository }}
      run: |
        oras push \
          "ghcr.io/${GITHUB_REPOSITORY}/matrix-stack:artifacthub.io" \
          --config /dev/null:application/vnd.cncf.artifacthub.config.v1+yaml \
          artifacthub-repo.yaml:application/vnd.cncf.artifacthub.repository-metadata.layer.v1.yaml
