# Copyright 2024-2025 New Vector Ltd
#
# SPDX-License-Identifier: AGPL-3.0-only

name: <PERSON><PERSON><PERSON> linting

on:
  pull_request_target:
  push:
    branches:
    - main
  workflow_dispatch:

jobs:
  shellcheck:
    runs-on: ubuntu-latest
    steps:
    - name: Checkout
      uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683  # v4
      with:
        ref: ${{ github.event.pull_request.head.sha }}

    - name: Run ShellCheck
      run: shellcheck scripts/*.sh

  python:
    runs-on: ubuntu-latest
    steps:
    - name: Checkout
      uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683  # v4
      with:
        ref: ${{ github.event.pull_request.head.sha }}

    - uses: matrix-org/setup-python-poetry@5bbf6603c5c930615ec8a29f1b5d7d258d905aa4  # v2
      with:
        poetry-version: "1.8.5"
        python-version: "3.x"

    - name: Load poetry path
      run: |
        echo "$(poetry env info -p)/bin" >> "${GITHUB_PATH}"

    - name: Run ruff check
      run: |
        ruff check

    - name: Run ruff format
      run: |
        ruff format --check

    - name: pytest scripts
      run: |
        pytest --version
        pytest scripts

    - name: mypy
      run:
        mypy tests
