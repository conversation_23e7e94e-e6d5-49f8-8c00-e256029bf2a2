# Copyright 2024-2025 New Vector Ltd
#
# SPDX-License-Identifier: AGPL-3.0-only
#
# source_fragments: matrix-authentication-service-minimal.yaml matrix-authentication-service-secrets-externally.yaml postgres-secrets-in-helm.yaml postgres-matrix-authentication-service-secrets-in-helm.yaml init-secrets-disabled.yaml
# DO NOT EDIT DIRECTLY. Edit the fragment files to add / modify / remove values

elementWeb:
  enabled: false
initSecrets:
  enabled: false
matrixAuthenticationService:
  encryptionSecret:
    secret: '{{ $.Release.Name }}-mas-external'
    secretKey: encryption
  ingress:
    host: mas.ess.localhost
  privateKeys:
    ecdsaPrime256v1:
      secret: '{{ $.Release.Name }}-mas-external'
      secretKey: keysEcdsaPrime256v1
    ecdsaSecp256k1:
      secret: '{{ $.Release.Name }}-mas-external'
      secretKey: keysEcdsaSecp256k1
    ecdsaSecp384r1:
      secret: '{{ $.Release.Name }}-mas-external'
      secretKey: keysEcdsaSecp384r1
    rsa:
      secret: '{{ $.Release.Name }}-mas-external'
      secretKey: keysRSA
  synapseOIDCClientSecret:
    secret: '{{ $.Release.Name }}-mas-external'
    secretKey: synapseOIDC
  synapseSharedSecret:
    secret: '{{ $.Release.Name }}-mas-external'
    secretKey: synapseShared
matrixRTC:
  enabled: false
postgres:
  adminPassword:
    value: CHANGEME-phiaPh8iu9tiivaiWahquaeg8ohcub4a
  essPasswords:
    matrixAuthenticationService:
      value: CHANGEME-aiT7eisheim3Ojo3rongikuo1eiV3Ooh
synapse:
  enabled: false
wellKnownDelegation:
  enabled: false
