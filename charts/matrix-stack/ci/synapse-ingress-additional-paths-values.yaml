# Copyright 2024-2025 New Vector Ltd
#
# SPDX-License-Identifier: AGPL-3.0-only
#
# source_fragments: synapse-minimal.yaml synapse-ingress-additional-paths.yaml init-secrets-minimal.yaml postgres-minimal.yaml
# DO NOT EDIT DIRECTLY. Edit the fragment files to add / modify / remove values

# initSecrets, postgres don't have any required properties to be set and defaults to enabled
elementWeb:
  enabled: false
matrixAuthenticationService:
  enabled: false
matrixRTC:
  enabled: false
serverName: ess.localhost
synapse:
  ingress:
    additionalPaths:
      - availability: only_externally
        path: /_matrix/identity
        service:
          name: sydent
          port:
            number: 8080
      - availability: blocked
        path: /_synapse
      - availability: internally_and_externally
        path: /other
        service:
          name: something
          port:
            name: http
    host: synapse.ess.localhost
wellKnownDelegation:
  enabled: false
