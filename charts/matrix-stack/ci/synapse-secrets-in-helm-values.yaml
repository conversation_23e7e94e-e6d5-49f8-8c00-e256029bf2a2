# Copyright 2024-2025 New Vector Ltd
#
# SPDX-License-Identifier: AGPL-3.0-only
#
# source_fragments: synapse-minimal.yaml synapse-postgres.yaml synapse-additional-in-helm.yaml synapse-postgres-secrets-in-helm.yaml synapse-secrets-in-helm.yaml
# DO NOT EDIT DIRECTLY. Edit the fragment files to add / modify / remove values

# initSecrets, postgres don't have any required properties to be set and defaults to enabled
elementWeb:
  enabled: false
matrixAuthenticationService:
  enabled: false
matrixRTC:
  enabled: false
serverName: ess.localhost
synapse:
  additional:
    00-userconfig.yaml:
      config: |
        push:
          jitter_dalay: 10
  ingress:
    host: synapse.ess.localhost
  macaroon:
    value: CHANGEME-eek3Eigoh8ux8laeTingeej1
  postgres:
    database: synapse
    host: ess-postgres
    password:
      value: CHANGEME-ooWo6jeidahhei3Hae0eer9U
    user: synapse_user
  registrationSharedSecret:
    value: CHANGEME-ooWo6jeidahhei3Hae0eer9U
  signingKey:
    value: ed25519 0 bNQOzBUDszff7Ax81z6w0uZ1IPWoxYaazT7emaZEfpw
wellKnownDelegation:
  enabled: false
