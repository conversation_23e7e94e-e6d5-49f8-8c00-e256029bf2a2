# Copyright 2024-2025 New Vector Ltd
#
# SPDX-License-Identifier: AGPL-3.0-only
#
# source_fragments: element-web-minimal.yaml element-web-pytest-extras.yaml
# DO NOT EDIT DIRECTLY. Edit the fragment files to add / modify / remove values

# initSecrets, postgres don't have any required properties to be set and defaults to enabled
elementWeb:
  additional:
    user-config.json: |
      {
        "default_server_config": {
          "m.homeserver": {
            "base_url": "https://synapse.{{ $.Values.serverName }}"
          }
        },
        "some_key": {
          "some_value": "https://test.{{ $.Values.serverName }}"
        }
      }
  annotations:
    has-no-service-monitor: "true"
  ingress:
    host: element.{{ $.Values.serverName }}
    tlsSecret: '{{ $.Release.Name }}-element-web-tls'
  podSecurityContext:
    runAsGroup: 0
  replicas: 2
matrixAuthenticationService:
  enabled: false
matrixRTC:
  enabled: false
serverName: ess.localhost
synapse:
  enabled: false
wellKnownDelegation:
  enabled: false
