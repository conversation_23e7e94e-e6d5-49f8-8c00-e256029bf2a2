# Copyright 2024-2025 New Vector Ltd
#
# SPDX-License-Identifier: AGPL-3.0-only
#
# source_fragments: matrix-rtc-minimal.yaml matrix-rtc-external-livekit.yaml matrix-rtc-external-livekit-secrets-in-helm.yaml
# DO NOT EDIT DIRECTLY. Edit the fragment files to add / modify / remove values

# initSecrets, postgres don't have any required properties to be set and defaults to enabled
elementWeb:
  enabled: false
matrixAuthenticationService:
  enabled: false
matrixRTC:
  extraEnv:
    - name: LIVEKIT_URL
      value: wss://demo.livekit.cloud
  ingress:
    host: mrtc.ess.localhost
  livekitAuth:
    keysYaml:
      value: |
        CHANGEME-ooShei6Aebe0mesheicooCoo8Juuceke: CHANGEME-deiv8au0poecheamusohZe7Fil9pogu4
  sfu:
    enabled: false
synapse:
  enabled: false
wellKnownDelegation:
  enabled: false
