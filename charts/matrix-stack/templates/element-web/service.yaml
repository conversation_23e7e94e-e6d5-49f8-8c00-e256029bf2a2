{{- /*
Copyright 2024 New Vector Ltd

SPDX-License-Identifier: AGPL-3.0-only
*/ -}}
{{- with .Values.elementWeb -}}
{{- if .enabled -}}
apiVersion: v1
kind: Service
metadata:
  labels:
    {{- include "element-io.element-web.labels" (dict "root" $ "context" .) | nindent 4 }}
  name: {{ $.Release.Name }}-element-web
  namespace: {{ $.Release.Namespace }}
spec:
  type: {{ .ingress.service.type | default $.Values.ingress.service.type }}
  ports:
  - port: 80
    targetPort: element
    name: web
  selector:
    app.kubernetes.io/instance: {{ $.Release.Name }}-element-web
{{- end }}
{{- end }}
