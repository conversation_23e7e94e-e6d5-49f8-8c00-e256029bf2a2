{{- /*
Copyright 2024 New Vector Ltd

SPDX-License-Identifier: AGPL-3.0-only
*/ -}}

{{- with .Values.synapse -}}
{{- if .enabled -}}
{{- if (include "element-io.synapse.enabledWorkers" (dict "root" $)) | from<PERSON>son }}
{{- with .redis -}}
{{- include "element-io.ess-library.serviceAccount" (dict "root" $ "context" (dict "componentValues" . "nameSuffix" "synapse-redis")) }}
{{- end }}
{{- end -}}
{{- end -}}
{{- end -}}
