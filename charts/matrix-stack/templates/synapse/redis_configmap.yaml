{{- /*
Copyright 2024 New Vector Ltd

SPDX-License-Identifier: AGPL-3.0-only
*/ -}}

{{- with .Values.synapse -}}
{{- if .enabled -}}
{{- if (include "element-io.synapse.enabledWorkers" (dict "root" $)) | fromJson }}
{{- with .redis -}}
apiVersion: v1
kind: ConfigMap
metadata:
  labels:
    {{- include "element-io.synapse-redis.labels" (dict "root" $ "context" .) | nindent 4 }}
  name: {{ $.Release.Name }}-synapse-redis
  namespace: {{ $.Release.Namespace }}
data:
  {{- include "element-io.synapse-redis.configmap-data" (dict "root" $) | nindent 2 }}
{{- end }}
{{- end -}}
{{- end -}}
{{- end -}}
