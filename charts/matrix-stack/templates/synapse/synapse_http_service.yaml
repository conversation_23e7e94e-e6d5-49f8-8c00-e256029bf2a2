{{- /*
Copyright 2024 New Vector Ltd

SPDX-License-Identifier: AGPL-3.0-only
*/ -}}

{{- with $.Values.synapse -}}
{{- if .enabled -}}
apiVersion: v1
kind: Service
metadata:
  labels:
    {{- include "element-io.synapse-ingress.labels" (dict "root" $ "context" $.Values.haproxy) | nindent 4 }}
  name: {{ $.Release.Name }}-synapse
  namespace: {{ $.Release.Namespace }}
spec:
  type: {{ .ingress.service.type | default $.Values.ingress.service.type }}
  ports:
  - name: haproxy-synapse
    port: 8008
    targetPort: haproxy-synapse
  - name: haproxy-403
    port: 8009
    targetPort: haproxy-403
  selector:
    app.kubernetes.io/instance: "{{ $.Release.Name }}-haproxy"
{{- end -}}
{{- end -}}
