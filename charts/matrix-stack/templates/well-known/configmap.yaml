{{- /*
Copyright 2023-2024 New Vector Ltd

SPDX-License-Identifier: AGPL-3.0-only
*/ -}}

{{- with $.Values.wellKnownDelegation -}}
{{- if .enabled -}}
apiVersion: v1
kind: ConfigMap
metadata:
  labels:
    {{- include "element-io.well-known-delegation.labels" (dict "root" $ "context" .) | nindent 4 }}
  name: {{ $.Release.Name }}-well-known-haproxy
  namespace: {{ $.Release.Namespace }}
data:
  {{- include "element-io.well-known-delegation.configmap-data" (dict "root" $ "context" .) | nindent 2 }}
{{- end -}}
{{- end -}}
