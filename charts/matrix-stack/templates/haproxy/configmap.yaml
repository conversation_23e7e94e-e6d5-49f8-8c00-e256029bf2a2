{{- /*
Copyright 2024 New Vector Ltd

SPDX-License-Identifier: AGPL-3.0-only
*/ -}}

{{- if or $.Values.synapse.enabled $.Values.wellKnownDelegation.enabled -}}
{{- with .Values.haproxy -}}
apiVersion: v1
kind: ConfigMap
metadata:
  labels:
    {{- include "element-io.haproxy.labels" (dict "root" $ "context" .) | nindent 4 }}
  name: {{ $.Release.Name }}-haproxy
  namespace: {{ $.Release.Namespace }}
data:
  {{- include "element-io.haproxy.configmap-data" (dict "root" $ "context" .) | nindent 2 -}}
{{- end -}}
{{- end -}}
