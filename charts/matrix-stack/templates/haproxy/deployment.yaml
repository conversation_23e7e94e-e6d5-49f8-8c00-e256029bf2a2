{{- /*
Copyright 2024-2025 New Vector Ltd

SPDX-License-Identifier: AGPL-3.0-only
*/ -}}


{{- if or $.Values.synapse.enabled $.Values.wellKnownDelegation.enabled -}}
{{- with .Values.haproxy -}}
apiVersion: apps/v1
kind: Deployment
metadata:
{{- with .annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
{{- end }}
  labels:
    {{- include "element-io.haproxy.labels" (dict "root" $ "context" .) | nindent 4 }}
    k8s.element.io/shared-haproxy-config-hash: "{{ include "element-io.haproxy.configmap-data" (dict "root" $ "context" .) | sha1sum }}"
{{- if $.Values.synapse.enabled }}
    k8s.element.io/synapse-haproxy-config-hash: "{{ include "element-io.synapse-haproxy.configmap-data" (dict "root" $) | sha1sum }}"
{{- end }}
{{- if $.Values.wellKnownDelegation.enabled }}
    k8s.element.io/wellknowndelegation-haproxy-config-hash: {{ include "element-io.well-known-delegation.configmap-data" (dict "root" $ "context" $.Values.wellKnownDelegation) | sha1sum }}
{{- end }}
  name: {{ $.Release.Name }}-haproxy
  namespace: {{ $.Release.Namespace }}
spec:
  {{ include "element-io.ess-library.deployments.commonSpec" (dict "root" $ "context" (dict "replicas" .replicas "nameSuffix" "haproxy")) | nindent 2 }}
  template:
    metadata:
      labels:
        {{- include "element-io.haproxy.labels" (dict "root" $ "context" (dict "image" .image "labels" .labels "withChartVersion" false)) | nindent 8 }}
        k8s.element.io/shared-haproxy-config-hash: {{ include "element-io.haproxy.configmap-data" (dict "root" $ "context" .) | sha1sum }}
{{- if $.Values.synapse.enabled }}
        k8s.element.io/synapse-haproxy-config-hash: {{ include "element-io.synapse-haproxy.configmap-data" (dict "root" $) | sha1sum }}
{{- end }}
{{- if $.Values.wellKnownDelegation.enabled }}
        k8s.element.io/wellknowndelegation-haproxy-config-hash: {{ include "element-io.well-known-delegation.configmap-data" (dict "root" $ "context" $.Values.wellKnownDelegation) | sha1sum }}
{{- end }}
{{- with .annotations }}
      annotations:
        {{- toYaml . | nindent 8 }}
{{- end }}
    spec:
{{- include "element-io.ess-library.pods.commonSpec" (dict "root" $ "context" (dict "componentValues" . "instanceSuffix" "haproxy" "deployment" true)) | nindent 6 }}
      containers:
      - name: haproxy
        args:
        - "-f"
        - "/usr/local/etc/haproxy/haproxy.cfg"
        - "-dW"
{{- with .image -}}
{{- if .digest }}
        image: "{{ .registry }}/{{ .repository }}@{{ .digest }}"
        imagePullPolicy: {{ .pullPolicy | default "IfNotPresent" }}
{{- else }}
        image: "{{ .registry }}/{{ .repository }}:{{ .tag }}"
        imagePullPolicy: {{ .pullPolicy | default "Always" }}
{{- end }}
{{- end }}
{{- with .containersSecurityContext }}
        securityContext:
          {{- toYaml . | nindent 10 }}
{{- end }}
        {{- include "element-io.ess-library.pods.env" (dict "root" $ "context" (dict "componentValues" . "componentName" "haproxy")) | nindent 8 }}
        ports:
{{- if $.Values.synapse.enabled }}
        - containerPort: 8008
          name: haproxy-synapse
          protocol: TCP
        - containerPort: 8009
          name: haproxy-403
          protocol: TCP
{{- end }}
{{- if $.Values.wellKnownDelegation.enabled }}
        - containerPort: 8010
          name: haproxy-wkd
          protocol: TCP
{{- end }}
        - containerPort: 8405
          name: haproxy-metrics
          protocol: TCP
{{- if $.Values.synapse.enabled }}
        - containerPort: 8406
          name: synapse-ready
          protocol: TCP
{{- end }}
        startupProbe: {{- include "element-io.ess-library.pods.probe" .startupProbe | nindent 10 }}
          httpGet:
{{- if $.Values.synapse.enabled }}
            path: /synapse_ready
            port: synapse-ready
{{- else }}
            path: /haproxy_test
            port: haproxy-metrics
{{- end }}
        livenessProbe: {{- include "element-io.ess-library.pods.probe" .livenessProbe | nindent 10 }}
          httpGet:
            path: /haproxy_test
            port: haproxy-metrics
        readinessProbe: {{- include "element-io.ess-library.pods.probe" .readinessProbe | nindent 10 }}
          httpGet:
            path: /haproxy_test
            port: haproxy-metrics
{{- with .resources }}
        resources:
          {{- toYaml . | nindent 10 }}
{{- end }}
        volumeMounts:
        - name: haproxy-config
          mountPath: "/usr/local/etc/haproxy"
          readOnly: true
{{- if $.Values.synapse.enabled }}
        - name: synapse-haproxy
          mountPath: "/synapse"
          readOnly: true
{{- end }}
{{- if $.Values.wellKnownDelegation.enabled }}
        - name: well-known-haproxy
          mountPath: "/well-known"
          readOnly: true
{{- end }}
      volumes:
      - configMap:
          name: "{{ $.Release.Name }}-haproxy"
          defaultMode: 420
        name: haproxy-config
{{- if $.Values.synapse.enabled }}
      - configMap:
          name: "{{ $.Release.Name }}-synapse-haproxy"
          defaultMode: 420
        name: synapse-haproxy
{{- end }}
{{- if $.Values.wellKnownDelegation.enabled }}
      - configMap:
          name: "{{ $.Release.Name }}-well-known-haproxy"
          defaultMode: 420
        name: well-known-haproxy
{{- end }}
{{- end }}
{{- end }}
