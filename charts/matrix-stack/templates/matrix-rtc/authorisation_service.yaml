{{- /*
Copyright 2024-2025 New Vector Ltd

SPDX-License-Identifier: AGPL-3.0-only
*/ -}}

{{- with $.Values.matrixRTC -}}
{{- if .enabled -}}
apiVersion: v1
kind: Service
metadata:
  labels:
    {{- include "element-io.matrix-rtc-authorisation-service.labels" (dict "root" $ "context" .) | nindent 4 }}
  name: {{ $.Release.Name }}-matrix-rtc-authorisation-service
  namespace: {{ $.Release.Namespace }}
spec:
  type: ClusterIP
  ports:
  - name: http
    port: 8080
    targetPort: http
  selector:
    app.kubernetes.io/instance: "{{ $.Release.Name }}-matrix-rtc-authorisation-service"
{{- end -}}
{{- end -}}
