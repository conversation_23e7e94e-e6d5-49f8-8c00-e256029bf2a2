{{- /*
Copyright 2024 New Vector Ltd

SPDX-License-Identifier: AGPL-3.0-only
*/ -}}

{{- with $.Values.matrixRTC -}}
{{- if .enabled -}}
{{- with .sfu -}}
{{- if .enabled -}}
apiVersion: v1
kind: ConfigMap
metadata:
  labels:
    {{- include "element-io.matrix-rtc-sfu.labels" (dict "root" $ "context" .) | nindent 4 }}
  name: {{ $.Release.Name }}-matrix-rtc-sfu
  namespace: {{ $.Release.Namespace }}
data:
  {{- (include "element-io.matrix-rtc-sfu.configmap-data" (dict "root" $ "context" .)) | nindent 2 }}
{{- end -}}
{{- end -}}
{{- end -}}
{{- end -}}
