{{- /*
Copyright 2024-2025 New Vector Ltd

SPDX-License-Identifier: AGPL-3.0-only
*/ -}}

{{- /* Returns the value (or nill) encoded as JSO<PERSON> at the given dot seperated path in the values file */ -}}
{{- define "element-io.ess-library.value-from-values-path" -}}
{{- $root := .root -}}
{{- with required "element-io.ess-library.value-from-values-path missing context" .context -}}
{{- $path := . -}}
{{- $navigatedToPart := mustMergeOverwrite (dict) (mustDeepCopy $root.Values) -}}
{{- range (mustRegexSplit "\\." $path -1) -}}
{{- if $navigatedToPart -}}
{{- $navigatedToPart = dig . nil $navigatedToPart -}}
{{- end -}}
{{- end -}}
{{ $navigatedToPart | toJson }}
{{- end -}}
{{- end -}}

{{- define "element-io.ess-library.check-credential" -}}
{{- $root := .root -}}
{{- with required "element-io.ess-library.check-credential missing context" .context -}}
{{- $secretPath := required "element-io.ess-library.check-credential context missing secretPath" .secretPath -}}
{{- $initIfAbsent := required "element-io.ess-library.check-credential context missing initIfAbsent" .initIfAbsent -}}
{{- $secretProperty := include "element-io.ess-library.value-from-values-path" (dict "root" $root "context" $secretPath) | fromJson -}}
{{- if and ($secretProperty).value (or ($secretProperty).secret ($secretProperty).secretKey) -}}
{{- fail (printf "The Secret configuration at %s sets both value and secret/secretKey. Only one can be set" $secretPath) -}}
{{- else if and ($secretProperty).secret (not ($secretProperty).secretKey) -}}
{{- fail (printf "The Secret configuration at %s has a secret but no secretKey property" $secretPath) -}}
{{- else if and ($secretProperty).secretKey (not ($secretProperty).secret) -}}
{{- fail (printf "The Secret configuration at %s has a secretKey but no secret property" $secretPath) -}}
{{- else if and ($secretProperty).secret ($secretProperty).secretKey -}}
{{- /* OK secret has a secret and a secretKey, do nothing */ -}}
{{- else if ($secretProperty).value -}}
{{- /* OK secret has a value, do nothing */ -}}
{{- else if not $initIfAbsent -}}
{{- fail (printf "The Secret configuration at %s is not present and the credential can't be autogenerated" $secretPath) -}}
{{- else if $root.Values.initSecrets.enabled -}}
{{- /* OK secret is generatable and initSecrets is on */ -}}
{{- else -}}
{{- fail (printf "check-credential: initSecrets is disabled, but the Secret configuration at %s is not present" $secretPath) -}}
{{- end -}}
{{- end -}}
{{- end }}

{{- define "element-io.ess-library.init-secret-path" -}}
{{- $root := .root -}}
{{- with required "element-io.ess-library.init-secret-path" .context -}}
{{- $secretPath := required "element-io.ess-library.init-secret-path context missing secretPath" .secretPath -}}
{{- $initSecretKey := required "element-io.ess-library.init-secret-path context missing initSecretKey" .initSecretKey -}}
{{- $defaultSecretName := required "element-io.ess-library.init-secret-path context missing defaultSecretName" .defaultSecretName -}}
{{- $defaultSecretKey := required "element-io.ess-library.init-secret-path context missing defaultSecretKey" .defaultSecretKey -}}
{{- $secretProperty := include "element-io.ess-library.value-from-values-path" (dict "root" $root "context" $secretPath) | fromJson -}}
{{- if not $secretProperty -}}
  {{- if $root.Values.initSecrets.enabled -}}
  {{- printf "%s/%s" (printf "%s-generated" $root.Release.Name) $initSecretKey -}}
  {{- else -}}
  {{- fail (printf "init-secret-path: initSecrets is disabled, but the Secret configuration at %s is not present" $secretPath) -}}
  {{- end -}}
{{- else -}}
  {{- include "element-io.ess-library.provided-secret-path" (dict "root" $root "context" (dict "secretPath" $secretPath "defaultSecretName" $defaultSecretName "defaultSecretKey" $defaultSecretKey)) -}}
{{- end -}}
{{- end -}}
{{- end -}}


{{- define "element-io.ess-library.provided-secret-path" -}}
{{- $root := .root -}}
{{- with required "element-io.ess-library.provided-secret-path missing context" .context -}}
{{- $secretPath := required "element-io.ess-library.provided-secret-path context missing secretPath" .secretPath -}}
{{- $defaultSecretName := required "element-io.ess-library.provided-secret-path context missing defaultSecretName" .defaultSecretName -}}
{{- $defaultSecretKey := required "element-io.ess-library.provided-secret-path context missing defaultSecretKey" .defaultSecretKey -}}
{{- $secretProperty := include "element-io.ess-library.value-from-values-path" (dict "root" $root "context" $secretPath) | fromJson -}}
{{- if not $secretProperty -}}
{{- /* If the Secret is truely optional then calling the helper itself should be gated instead */ -}}
{{- fail (printf "The Secret configuration at %s is not present and the credential can't be autogenerated" $secretPath) -}}
{{- else -}}
{{- if $secretProperty.value -}}
{{- printf "%s/%s" $defaultSecretName $defaultSecretKey -}}
{{- else -}}
{{- printf "%s/%s" (tpl $secretProperty.secret $root) (tpl $secretProperty.secretKey $root) -}}
{{- end -}}
{{- end -}}
{{- end -}}
{{- end -}}


{{- define "element-io.ess-library.postgres-secret-path" -}}
{{- $root := .root -}}
{{- with required "element-io.ess-library.postgres-secret-path" .context -}}
{{- $componentPasswordPath := required "element-io.ess-library.postgres-secret-path context missing componentPasswordPath" .componentPasswordPath -}}
{{- $essPassword := required "element-io.ess-library.postgres-secret-path context missing essPassword" .essPassword -}}
{{- $initSecretKey := required "element-io.ess-library.postgres-secret-path context missing initSecretKey" .initSecretKey -}}
{{- $defaultSecretName := required "element-io.ess-library.postgres-secret-path context missing defaultSecretName" .defaultSecretName -}}
{{- $defaultSecretKey := required "element-io.ess-library.postgres-secret-path context missing defaultSecretKey" .defaultSecretKey -}}
{{- $isHook := required "element-io.ess-library.postgres-secret-path context missing isHook" .isHook -}}
{{- $secretProperty := include "element-io.ess-library.value-from-values-path" (dict "root" $root "context" $componentPasswordPath) | fromJson -}}
{{- if not $secretProperty -}}
  {{- if (not (index $root.Values.postgres.essPasswords $essPassword)) }}
    {{- if $root.Values.initSecrets.enabled -}}
    {{- printf "%s/%s" (printf "%s-generated" $root.Release.Name) $initSecretKey -}}
    {{- else -}}
    {{- fail (printf "postgres-secret-path: initSecrets is disabled, but the Secret configuration at %s is not present" $componentPasswordPath) -}}
    {{- end -}}
  {{- else -}}
    {{- include "element-io.ess-library.provided-secret-path" (dict
                  "root" $root
                  "context" (dict
                    "secretPath" (printf "postgres.essPasswords.%s" $essPassword)
                    "defaultSecretName" (include "element-io.postgres.secret-name" (dict "root" $root "context"  (dict "isHook" .isHook)))
                    "defaultSecretKey" (printf "ESS_PASSWORD_%s" ($essPassword | upper))
                  )
                )
    -}}
  {{- end -}}
{{- else -}}
  {{- include "element-io.ess-library.provided-secret-path" (dict
                "root" $root
                "context" (dict
                  "secretPath" $componentPasswordPath
                  "defaultSecretName" $defaultSecretName
                  "defaultSecretKey" $defaultSecretKey
                )
              )
    -}}
{{- end -}}
{{- end -}}
{{- end -}}


{{- define "element-io.ess-library.postgres-secret-name" -}}
{{- $root := .root -}}
{{- with required "element-io.ess-library.postgres-secret-name" .context -}}
{{- $essPassword := required "element-io.ess-library.postgres-secret-name context missing essPassword" .essPassword -}}
{{- $componentPasswordPath := required "element-io.ess-library.postgres-secret-name context missing componentPasswordPath" .componentPasswordPath -}}
{{- $defaultSecretName := required "element-io.ess-library.postgres-secret-name context missing defaultSecretName" .defaultSecretName -}}
{{- $isHook := required "element-io.ess-library.postgres-secret-name context missing isHook" .isHook -}}
{{- $secretProperty := include "element-io.ess-library.value-from-values-path" (dict "root" $root "context" $componentPasswordPath) | fromJson -}}
{{- if $secretProperty -}}
    {{- if $secretProperty.value -}}
    {{- $defaultSecretName -}}
    {{- else -}}
    {{- tpl $secretProperty.secret $root -}}
    {{- end -}}
{{- else if (index $root.Values.postgres.essPasswords $essPassword) }}
    {{- if (index $root.Values.postgres.essPasswords $essPassword).value -}}
    {{- include "element-io.postgres.secret-name" (dict "root" $root "context"  (dict "isHook" .isHook)) }}
    {{- else -}}
    {{- tpl (index $root.Values.postgres.essPasswords $essPassword).secret $root -}}
    {{- end -}}
{{- else -}}
  {{- if $root.Values.initSecrets.enabled -}}
  {{- printf "%s-generated" $root.Release.Name -}}
  {{- else -}}
  {{- fail (printf "postgres-secret-name: initSecrets is disabled, but the Secret configuration at %s is not present" $componentPasswordPath) -}}
  {{- end -}}
{{- end -}}
{{- end -}}
{{- end -}}
