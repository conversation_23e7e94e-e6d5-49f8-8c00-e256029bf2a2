{"type": "object", "properties": {"annotations": {"type": "object", "additionalProperties": {"type": ["string", "null"]}}, "host": {"type": "string"}, "className": {"type": "string"}, "tlsEnabled": {"type": "boolean"}, "tlsSecret": {"type": "string"}, "controllerType": {"type": "string", "enum": ["ingress-nginx"]}, "service": {"type": "object", "properties": {"type": {"type": "string", "enum": ["ClusterIP", "NodePort", "LoadBalancer"]}}}, "additionalPaths": {"type": "array", "items": {"type": "object", "required": ["path", "availability"], "properties": {"path": {"type": "string"}, "availability": {"type": "string", "enum": ["internally_and_externally", "only_externally", "blocked"]}, "service": {"type": "object", "required": ["name", "port"], "properties": {"name": {"type": "string"}, "port": {"type": "object", "oneOf": [{"required": ["name"], "not": {"required": ["number"]}}, {"required": ["number"], "not": {"required": ["name"]}}], "properties": {"name": {"type": "string"}, "number": {"type": "integer"}}}}}}}}}}