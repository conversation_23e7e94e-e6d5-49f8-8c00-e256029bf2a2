{"$id": "file://postgres", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "properties": {"enabled": {"type": "boolean"}, "image": {"$ref": "file://common/image.json"}, "postgresExporter": {"type": "object", "properties": {"image": {"$ref": "file://common/image.json"}, "extraEnv": {"$ref": "file://common/extraEnv.json"}, "containersSecurityContext": {"$ref": "file://common/containersSecurityContext.json"}, "resources": {"$ref": "file://common/resources.json"}, "livenessProbe": {"$ref": "file://common/probe.json"}, "readinessProbe": {"$ref": "file://common/probe.json"}, "startupProbe": {"$ref": "file://common/probe.json"}}}, "adminPassword": {"$ref": "file://common/credential.json"}, "essPasswords": {"type": "object", "properties": {"synapse": {"$ref": "file://common/credential.json"}, "matrixAuthenticationService": {"$ref": "file://common/credential.json"}}}, "storage": {"$ref": "file://common/persistentVolumeClaim.json"}, "labels": {"$ref": "file://common/labels.json"}, "annotations": {"$ref": "file://common/workloadAnnotations.json"}, "extraEnv": {"$ref": "file://common/extraEnv.json"}, "containersSecurityContext": {"$ref": "file://common/containersSecurityContext.json"}, "nodeSelector": {"$ref": "file://common/nodeSelector.json"}, "podSecurityContext": {"$ref": "file://common/podSecurityContext.json"}, "resources": {"$ref": "file://common/resources.json"}, "serviceAccount": {"$ref": "file://common/serviceAccount.json"}, "tolerations": {"$ref": "file://common/tolerations.json"}, "topologySpreadConstraints": {"$ref": "file://common/topologySpreadConstraints.json"}, "livenessProbe": {"$ref": "file://common/probe.json"}, "readinessProbe": {"$ref": "file://common/probe.json"}, "startupProbe": {"$ref": "file://common/probe.json"}, "serviceMonitors": {"$ref": "file://common/serviceMonitors.json"}}}