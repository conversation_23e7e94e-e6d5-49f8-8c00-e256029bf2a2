{"$id": "file://matrixAuthenticationService", "$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "properties": {"enabled": {"type": "boolean", "description": "Enable the Matrix Authentication Service (MAS) service."}, "additional": {"$ref": "file://common/additional.json"}, "encryptionSecret": {"$ref": "file://common/credential.json"}, "synapseSharedSecret": {"$ref": "file://common/credential.json"}, "synapseOIDCClientSecret": {"$ref": "file://common/credential.json"}, "postgres": {"$ref": "file://common/postgres-libpq.json"}, "privateKeys": {"default": {}, "type": "object", "description": "The private keys used for signing. Only RSA private key is required.", "properties": {"ecdsaPrime256v1": {"$ref": "file://common/credential.json"}, "ecdsaSecp256k1": {"$ref": "file://common/credential.json"}, "ecdsaSecp384r1": {"$ref": "file://common/credential.json"}, "rsa": {"$ref": "file://common/credential.json"}}}, "replicas": {"type": "integer"}, "syn2mas": {"type": "object", "properties": {"enabled": {"type": "boolean", "description": "Enable synapse to Matrix Authentication Service migration."}, "image": {"$ref": "file://common/image.json"}, "dryRun": {"type": "boolean", "description": "Run the migration job in dry-run mode. Do not actually migrate the data."}, "labels": {"$ref": "file://common/labels.json"}, "annotations": {"$ref": "file://common/workloadAnnotations.json"}, "extraEnv": {"$ref": "file://common/extraEnv.json"}, "containersSecurityContext": {"$ref": "file://common/containersSecurityContext.json"}, "nodeSelector": {"$ref": "file://common/nodeSelector.json"}, "podSecurityContext": {"$ref": "file://common/podSecurityContext.json"}, "resources": {"$ref": "file://common/resources.json"}, "serviceAccount": {"$ref": "file://common/serviceAccount.json"}, "tolerations": {"$ref": "file://common/tolerations.json"}}}, "ingress": {"$ref": "file://common/ingress.json"}, "image": {"$ref": "file://common/image.json"}, "labels": {"$ref": "file://common/labels.json"}, "annotations": {"$ref": "file://common/workloadAnnotations.json"}, "containersSecurityContext": {"$ref": "file://common/containersSecurityContext.json"}, "extraEnv": {"$ref": "file://common/extraEnv.json"}, "hostAliases": {"$ref": "file://common/hostAliases.json"}, "nodeSelector": {"$ref": "file://common/nodeSelector.json"}, "podSecurityContext": {"$ref": "file://common/podSecurityContext.json"}, "resources": {"$ref": "file://common/resources.json"}, "serviceAccount": {"$ref": "file://common/serviceAccount.json"}, "serviceMonitors": {"$ref": "file://common/serviceMonitors.json"}, "tolerations": {"$ref": "file://common/tolerations.json"}, "topologySpreadConstraints": {"$ref": "file://common/topologySpreadConstraints.json"}, "livenessProbe": {"$ref": "file://common/probe.json"}, "readinessProbe": {"$ref": "file://common/probe.json"}, "startupProbe": {"$ref": "file://common/probe.json"}}}