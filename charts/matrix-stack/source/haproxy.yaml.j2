{#
Copyright 2024-2025 New Vector Ltd

SPDX-License-Identifier: AGPL-3.0-only
#}

{% import 'sub_schema_values.yaml.j2' as sub_schema_values -%}

replicas: 1
{{- sub_schema_values.image(registry='docker.io', repository='library/haproxy', tag='3.1-alpine') }}
{{- sub_schema_values.labels() }}
{{- sub_schema_values.workloadAnnotations() }}
{{- sub_schema_values.containersSecurityContext() }}
{{- sub_schema_values.extraEnv() }}
{{- sub_schema_values.nodeSelector() }}
{{- sub_schema_values.podSecurityContext(user_id='10001', group_id='10001') }}
{{- sub_schema_values.resources(requests_memory='100Mi', requests_cpu='100m', limits_memory='200Mi') }}
{{- sub_schema_values.serviceAccount() }}
{{- sub_schema_values.serviceMonitors() }}
{{- sub_schema_values.tolerations() }}
{{- sub_schema_values.topologySpreadConstraints() }}
{{- sub_schema_values.probe("liveness", timeoutSeconds=5) }}
{{- sub_schema_values.probe("readiness", timeoutSeconds=5) }}
# The failureThreshold here is tweaked towards Synapse being ready
# If Synapse isn't being deployed, unsetting this or setting it to 3 maybe more appropriate
{{- sub_schema_values.probe("startup", failureThreshold=150, periodSeconds=2) }}
