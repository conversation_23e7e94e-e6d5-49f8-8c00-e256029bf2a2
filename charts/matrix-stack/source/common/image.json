{"type": "object", "required": ["repository"], "oneOf": [{"required": ["tag", "digest"]}, {"required": ["digest"], "not": {"required": ["tag"]}}, {"required": ["tag"], "not": {"required": ["digest"]}}], "properties": {"registry": {"type": "string"}, "repository": {"type": "string"}, "tag": {"type": ["string", "null"]}, "digest": {"type": ["string", "null"]}, "pullPolicy": {"type": "string", "enum": ["Always", "IfNotPresent", "Never"]}, "pullSecrets": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string"}}}}}}