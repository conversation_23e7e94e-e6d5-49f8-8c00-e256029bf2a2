# Copyright 2024-2025 New Vector Ltd
#
# SPDX-License-Identifier: AGPL-3.0-only

[tool.poetry]
description = ""
license = "AGPL-3.0-Only, Element Commercial"
readme = "README.md"
package-mode = false

[tool.poetry.dependencies]
python = ">=3.12,<4.0"

[tool.poetry.group.dev.dependencies]
pyhelm3 = "^0.4.0"
lightkube = "^0.17.2"
pytz = "^2025.2"
cryptography = "^45.0.4"
platformdirs = "^4.3.8"
python-on-whales = "^0.77.0"
aiohttp = "^3.12.13"
yamale = "^6.0.0"
typer = "^0.16.0"
pytest = "^8.4.0"
ruff = "^0.11.13"
jinja2 = "^3.1.6"
checkov = "^3.2.442"
reuse = "^5.0.2"
signedjson = "^1.1.4"
aiohttp-retry = "^2.9.1"
ruamel-yaml = "^0.18.14"
pytest-kubernetes = {git = "https://github.com/Blueshoe/pytest-kubernetes.git", rev = "feature/17_custom-configs"}
pytest-asyncio-cooperative ="^0.39.0"
towncrier = "^24.8.0"
spdx-tools = "^0.8.3"
types-pytz = "^2025.2.0.20250516"
mypy = "^1.16.1"
types-pyyaml = "^6.0.12.20250516"
semver = "^3.0.4"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.pytest.ini_options]
# we use asyncio-cooperative
addopts = "-p no:asyncio --strict-markers"

[tool.ruff]
line-length = 120

[tool.ruff.lint]
select = [
    # pycodestyle
    "E",
    # Pyflakes
    "F",
    # pyupgrade
    "UP",
    # flake8-bugbear
    "B",
    # flake8-simplify
    "SIM",
    # isort
    "I",
    # flake8-pytest-style
    "PT",
]

[tool.towncrier]
package = ""
name = "ESS Community Helm Chart"
filename = "CHANGELOG.md"

# See artifacthub changes kinds https://artifacthub.io/docs/topics/annotations/helm/
# "When using the list of objects option the valid supported kinds are added, changed, deprecated, removed, fixed and security."

[[tool.towncrier.type]]
directory = "security"
name = "Security"
showcontent = true

[[tool.towncrier.type]]
directory = "deprecated"
name = "Deprecated"
showcontent = true

[[tool.towncrier.type]]
directory = "added"
name = "Added"
showcontent = true

[[tool.towncrier.type]]
directory = "fixed"
name = "Fixed"
showcontent = true

[[tool.towncrier.type]]
directory = "changed"
name = "Changed"
showcontent = true

[[tool.towncrier.type]]
directory = "removed"
name = "Removed"
showcontent = true

[[tool.towncrier.type]]
directory = "internal"
name = "Internal"
showcontent = true

[[tool.mypy.overrides]]
module = ["pyhelm3.*", "pytest_kubernetes.*"]
follow_untyped_imports = true
